# Version control
.git
.gitignore
.gitattributes

# Environment files
.env
.env.*
*.env

# Python
__pycache__/
*.py[cod]
*.so
.Python
*.egg
*.egg-info/
.eggs/
*.pyc
.pytest_cache/
.coverage
htmlcov/
.tox/
.venv/
venv/

# Node.js
ui/node_modules/
ui/.npm
ui/npm-debug.log*
ui/yarn-debug.log*
ui/yarn-error.log*
ui/dist/

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
reports/*
.langgraph/
.elasticbeanstalk/
README.md
LICENSE
*.md
*.log 