@import url('https://fonts.googleapis.com/css2?family=DM+Sans:opsz,wght@9..40,400;9..40,500;9..40,600;9..40,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-900;
    font-family: "DM Sans", sans-serif;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.05) 1px, transparent 0);
    background-size: 24px 24px;
    background-position: center center;
  }

  h1 {
    font-family: "DM Sans", sans-serif;
    font-size: 48px;
    font-style: normal;
    font-variation-settings: normal;
    font-weight: 500;
    letter-spacing: -1px;
    text-rendering: optimizeLegibility;
    unicode-bidi: isolate;
    -webkit-font-smoothing: antialiased;
  }

  h2, h3, h4, h5, h6 {
    font-family: "DM Sans", sans-serif;
    font-weight: 500;
    letter-spacing: -0.5px;
  }

  p, span, div, li, a {
    font-family: "DM Sans", sans-serif;
    font-weight: 400;
  }

  input {
    font-family: "DM Sans", sans-serif;
  }

  button {
    font-family: "DM Sans", sans-serif;
  }

  select {
    font-family: "DM Sans", sans-serif;
  }
}

@layer components {
  .glass {
    @apply bg-gray-900/40 backdrop-blur-md border border-gray-700/50;
  }
}