{"name": "tavily-company-research-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "start": "serve -s build -l $PORT", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"framer-motion": "^12.4.10", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "serve": "^14.2.1"}, "devDependencies": {"@types/google.maps": "^3.58.1", "@types/node": "^22.13.9", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^6.3.4"}, "engines": {"node": ">=14.x"}}